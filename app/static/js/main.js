// JavaScript for Bijack FastAPI MVC App

// API base URL
const API_BASE_URL = '/api/users';

// Show create user modal
function showCreateUserModal() {
    document.getElementById('createUserForm').reset();
    const modal = new bootstrap.Modal(document.getElementById('createUserModal'));
    modal.show();
}

// Create new user
async function createUser() {
    const form = document.getElementById('createUserForm');
    const formData = new FormData(form);
    
    const userData = {
        username: document.getElementById('username').value,
        email: document.getElementById('email').value,
        full_name: document.getElementById('fullName').value,
        age: document.getElementById('age').value ? parseInt(document.getElementById('age').value) : null
    };

    try {
        const response = await fetch(API_BASE_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(userData)
        });

        if (response.ok) {
            const modal = bootstrap.Modal.getInstance(document.getElementById('createUserModal'));
            modal.hide();
            showAlert('User created successfully!', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            const error = await response.json();
            showAlert(error.detail || 'Error creating user', 'danger');
        }
    } catch (error) {
        showAlert('Network error occurred', 'danger');
        console.error('Error:', error);
    }
}

// Edit user
async function editUser(userId) {
    try {
        const response = await fetch(`${API_BASE_URL}/${userId}`);
        if (response.ok) {
            const user = await response.json();
            
            // Populate edit form
            document.getElementById('editUserId').value = user.id;
            document.getElementById('editUsername').value = user.username;
            document.getElementById('editEmail').value = user.email;
            document.getElementById('editFullName').value = user.full_name;
            document.getElementById('editAge').value = user.age || '';
            
            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('editUserModal'));
            modal.show();
        } else {
            showAlert('Error loading user data', 'danger');
        }
    } catch (error) {
        showAlert('Network error occurred', 'danger');
        console.error('Error:', error);
    }
}

// Update user
async function updateUser() {
    const userId = document.getElementById('editUserId').value;
    
    const userData = {
        username: document.getElementById('editUsername').value,
        email: document.getElementById('editEmail').value,
        full_name: document.getElementById('editFullName').value,
        age: document.getElementById('editAge').value ? parseInt(document.getElementById('editAge').value) : null
    };

    try {
        const response = await fetch(`${API_BASE_URL}/${userId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(userData)
        });

        if (response.ok) {
            const modal = bootstrap.Modal.getInstance(document.getElementById('editUserModal'));
            modal.hide();
            showAlert('User updated successfully!', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            const error = await response.json();
            showAlert(error.detail || 'Error updating user', 'danger');
        }
    } catch (error) {
        showAlert('Network error occurred', 'danger');
        console.error('Error:', error);
    }
}

// Delete user
async function deleteUser(userId) {
    if (confirm('Are you sure you want to delete this user?')) {
        try {
            const response = await fetch(`${API_BASE_URL}/${userId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                showAlert('User deleted successfully!', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert('Error deleting user', 'danger');
            }
        } catch (error) {
            showAlert('Network error occurred', 'danger');
            console.error('Error:', error);
        }
    }
}

// Show alert message
function showAlert(message, type) {
    const alertContainer = document.createElement('div');
    alertContainer.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertContainer.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertContainer.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertContainer);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (alertContainer.parentNode) {
            alertContainer.parentNode.removeChild(alertContainer);
        }
    }, 5000);
}

// Initialize tooltips and other Bootstrap components
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Add form validation
    const forms = document.querySelectorAll('.needs-validation');
    Array.prototype.slice.call(forms).forEach(function (form) {
        form.addEventListener('submit', function (event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
});

// Handle form submissions with Enter key
document.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        const activeModal = document.querySelector('.modal.show');
        if (activeModal) {
            if (activeModal.id === 'createUserModal') {
                createUser();
            } else if (activeModal.id === 'editUserModal') {
                updateUser();
            }
        }
    }
});
