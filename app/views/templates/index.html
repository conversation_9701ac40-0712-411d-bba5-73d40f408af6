{% extends "base.html" %}

{% block title %}{{ title }} - Bijack{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="jumbotron bg-primary text-white p-5 rounded mb-4">
            <h1 class="display-4">Welcome to Bijack!</h1>
            <p class="lead">A FastAPI application built with MVC architecture</p>
            <hr class="my-4">
            <p>This is a demonstration of a FastAPI application using the Model-View-Controller pattern.</p>
            <a class="btn btn-light btn-lg" href="/users" role="button">View Users</a>
            <a class="btn btn-outline-light btn-lg ms-2" href="/docs" target="_blank" role="button">API Documentation</a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-users"></i> Users
                </h5>
                <p class="card-text">Total registered users: <strong>{{ users_count }}</strong></p>
                <a href="/users" class="btn btn-primary">Manage Users</a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-code"></i> API
                </h5>
                <p class="card-text">RESTful API endpoints for all operations</p>
                <a href="/docs" target="_blank" class="btn btn-success">View API Docs</a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-database"></i> Database
                </h5>
                <p class="card-text">SQLAlchemy ORM with SQLite database</p>
                <a href="/api/users" class="btn btn-info">View JSON Data</a>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <h3>Features</h3>
        <ul class="list-group">
            <li class="list-group-item">
                <strong>MVC Architecture:</strong> Clean separation of concerns with Models, Views, and Controllers
            </li>
            <li class="list-group-item">
                <strong>FastAPI:</strong> Modern, fast web framework for building APIs with Python
            </li>
            <li class="list-group-item">
                <strong>SQLAlchemy ORM:</strong> Database operations with object-relational mapping
            </li>
            <li class="list-group-item">
                <strong>Jinja2 Templates:</strong> Server-side rendering with template inheritance
            </li>
            <li class="list-group-item">
                <strong>Pydantic Models:</strong> Data validation and serialization
            </li>
            <li class="list-group-item">
                <strong>Bootstrap UI:</strong> Responsive design with Bootstrap 5
            </li>
        </ul>
    </div>
</div>
{% endblock %}
