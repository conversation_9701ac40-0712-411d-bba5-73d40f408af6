from sqlalchemy import Column, String, Integer
from pydantic import BaseModel, EmailStr
from typing import Optional
from datetime import datetime

from app.models.base import BaseModel as DBBaseModel


# SQLAlchemy Model
class User(DBBaseModel):
    __tablename__ = "users"
    
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    full_name = Column(String(100), nullable=False)
    age = Column(Integer, nullable=True)


# Pydantic Models for API
class UserBase(BaseModel):
    username: str
    email: EmailStr
    full_name: str
    age: Optional[int] = None


class UserCreate(UserBase):
    pass


class UserUpdate(BaseModel):
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    age: Optional[int] = None


class UserResponse(UserBase):
    id: int
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True
