from fastapi import APIRouter, Request, Depends
from fastapi.templating import <PERSON><PERSON>2Templates
from fastapi.responses import HTMLResponse
from sqlalchemy.orm import Session

from app.config.database import get_db
from app.models.user import User

router = APIRouter()
templates = Jinja2Templates(directory="app/views/templates")


@router.get("/", response_class=HTMLResponse)
async def home(request: Request, db: Session = Depends(get_db)):
    """Render the home page"""
    users_count = db.query(User).count()
    return templates.TemplateResponse(
        "index.html", 
        {
            "request": request, 
            "title": "Welcome to Bijack", 
            "users_count": users_count
        }
    )


@router.get("/users", response_class=HTMLResponse)
async def users_page(request: Request, db: Session = Depends(get_db)):
    """Render the users page"""
    users = db.query(User).filter(User.is_active == True).all()
    return templates.TemplateResponse(
        "users.html", 
        {
            "request": request, 
            "title": "Users", 
            "users": users
        }
    )
