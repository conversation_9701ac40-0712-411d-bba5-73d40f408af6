# Bijack FastAPI MVC Application

A modern web application built with FastAPI using the Model-View-Controller (MVC) architectural pattern.

## Features

- **MVC Architecture**: Clean separation of concerns with Models, Views, and Controllers
- **FastAPI Framework**: Modern, fast web framework for building APIs with Python
- **SQLAlchemy ORM**: Database operations with object-relational mapping
- **Jinja<PERSON> Templates**: Server-side rendering with template inheritance
- **Pydantic Models**: Data validation and serialization
- **Bootstrap UI**: Responsive design with Bootstrap 5
- **RESTful API**: Complete CRUD operations with automatic API documentation
- **Database Integration**: SQLite database with easy migration to other databases

## Project Structure

```
/Users/<USER>/Desktop/Bijack/
├── app/
│   ├── __init__.py
│   ├── main.py                 # Main FastAPI application
│   ├── models/
│   │   ├── __init__.py
│   │   ├── base.py            # Base model class
│   │   └── user.py            # User model and Pydantic schemas
│   ├── controllers/
│   │   ├── __init__.py
│   │   ├── user_controller.py  # User API endpoints
│   │   └── home_controller.py  # Web page routes
│   ├── views/
│   │   └── templates/
│   │       ├── base.html      # Base template
│   │       ├── index.html     # Home page template
│   │       └── users.html     # Users management template
│   ├── config/
│   │   ├── __init__.py
│   │   └── database.py        # Database configuration
│   └── static/
│       ├── css/
│       │   └── style.css      # Custom styles
│       └── js/
│           └── main.js        # Frontend JavaScript
├── requirements.txt           # Python dependencies
├── .env                      # Environment variables
├── run.py                    # Application runner script
└── README.md                 # This file
```

## Installation and Setup

1. **Clone or navigate to the project directory:**
   ```bash
   cd /Users/<USER>/Desktop/Bijack
   ```

2. **Create and activate virtual environment:**
   ```bash
   python -m venv .venv
   source .venv/bin/activate  # On macOS/Linux
   ```

3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Run the application:**
   ```bash
   python run.py
   ```
   Or using uvicorn directly:
   ```bash
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

## Usage

After running the application, you can access:

- **Web Interface**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Alternative API Docs**: http://localhost:8000/redoc

### Web Interface

- **Home Page** (`/`): Overview of the application and features
- **Users Page** (`/users`): Manage users with a web interface

### API Endpoints

- `GET /api/users/` - Get all users
- `GET /api/users/{user_id}` - Get a specific user
- `POST /api/users/` - Create a new user
- `PUT /api/users/{user_id}` - Update an existing user
- `DELETE /api/users/{user_id}` - Delete a user (soft delete)

### Example API Usage

**Create a new user:**
```bash
curl -X POST "http://localhost:8000/api/users/" \
     -H "Content-Type: application/json" \
     -d '{
       "username": "johndoe",
       "email": "<EMAIL>",
       "full_name": "John Doe",
       "age": 30
     }'
```

**Get all users:**
```bash
curl -X GET "http://localhost:8000/api/users/"
```

## Database

The application uses SQLite by default. The database file (`bijack.db`) will be created automatically when you first run the application.

To use a different database (like PostgreSQL or MySQL), update the `DATABASE_URL` in your `.env` file:

```env
# For PostgreSQL
DATABASE_URL=postgresql://username:password@localhost/dbname

# For MySQL
DATABASE_URL=mysql://username:password@localhost/dbname
```

## MVC Architecture

### Models (`app/models/`)
- Define database schema using SQLAlchemy ORM
- Pydantic models for data validation and serialization
- Base model with common fields (id, created_at, updated_at, is_active)

### Views (`app/views/templates/`)
- Jinja2 templates for server-side rendering
- Template inheritance for consistent UI
- Bootstrap 5 for responsive design

### Controllers (`app/controllers/`)
- Handle HTTP requests and responses
- Business logic and data processing
- Separate controllers for web pages and API endpoints

## Configuration

Environment variables can be set in the `.env` file:

- `DATABASE_URL`: Database connection string
- `APP_NAME`: Application name
- `APP_VERSION`: Application version
- `DEBUG`: Enable/disable debug mode
- `SECRET_KEY`: Secret key for security
- `ALLOWED_ORIGINS`: CORS allowed origins

## Development

### Adding New Models

1. Create a new model file in `app/models/`
2. Define SQLAlchemy model inheriting from `BaseModel`
3. Create Pydantic schemas for validation
4. Import and register in `app/main.py`

### Adding New Controllers

1. Create a new controller file in `app/controllers/`
2. Define FastAPI router with endpoints
3. Include router in `app/main.py`

### Adding New Templates

1. Create template files in `app/views/templates/`
2. Extend `base.html` for consistent layout
3. Reference templates in controller functions

## License

This project is open source and available under the MIT License.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Support

For questions or issues, please create an issue in the project repository.
